import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';

// Context7 MCP: Use unified location providers instead of separate permission providers
import '../../../../core/providers/location_providers.dart';
import '../../../../core/providers/master_location_provider.dart';
import '../../../../core/utils/l10n.dart';
import '../../../../core/utils/result.dart';
import '../../../tutorial/domain/models/tutorial_target.dart';
import '../../../tutorial/domain/providers/tutorial_keys_provider.dart';
import '../providers/unified_nearby_masjids_provider.dart';

import 'nearby_masjids_dialog.dart';

/// A button widget that displays nearby masjids count and handles location permissions
/// Extracted from OptimizedMapWidget for better reusability and separation of concerns
class NearbyMasjidsButton extends ConsumerWidget {
  /// Optional callback when button is tapped
  final VoidCallback? onTap;

  /// Constructor
  const NearbyMasjidsButton({super.key, this.onTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the nearby masjids count
    final nearbyCountAsync = ref.watch(nearbyMasjidsCountProvider);

    return nearbyCountAsync.when(
      data: (count) => _buildNearbyButton(context, ref, count),
      loading: () => _buildNearbyButton(context, ref, 0),
      error: (error, stack) => _buildNearbyButton(context, ref, 0),
    );
  }

  /// Build the actual nearby button with count
  Widget _buildNearbyButton(BuildContext context, WidgetRef ref, int count) {
    return Material(
      key: ref.watch(tutorialKeysProvider)[TutorialTargetType.nearbyButton],
      color: const Color.fromARGB(0, 167, 35, 35),
      child: InkWell(
        borderRadius: _kBorderRadius,
        onTap: () => _handleNearbyMasjidsButtonTap(context, ref),
        child: Container(
          padding: _kButtonPadding,
          decoration: _buildButtonDecoration(),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                count > 0
                    ? '${context.l10n?.nearbyMasjids ?? 'Nearby Masjids'} ($count)'
                    : context.l10n?.nearbyMasjids ?? 'Nearby Masjids',
                style: _kButtonTextStyle,
              ),
              if (count > 0) ..._buildArrowIcon(),
            ],
          ),
        ),
      ),
    );
  }

  /// Const decoration for button
  static BoxDecoration _buildButtonDecoration() {
    return BoxDecoration(
      color: Colors.white.withValues(alpha: 0.8),
      borderRadius: _kBorderRadius,
      boxShadow: _kBoxShadow,
    );
  }

  /// Const arrow icon widgets
  static List<Widget> _buildArrowIcon() {
    return const [SizedBox(width: 4), Icon(Icons.arrow_forward_ios, size: 12, color: Colors.black54)];
  }

  // Const values for better performance
  static const _kBorderRadius = BorderRadius.all(Radius.circular(10));
  static const _kButtonPadding = EdgeInsets.symmetric(horizontal: 20, vertical: 12);
  static const _kButtonTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: Color.fromARGB(255, 44, 112, 168),
  );
  static const _kBoxShadow = [
    BoxShadow(
      color: Color.fromARGB(25, 0, 0, 0), // ~0.1 opacity
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];

  /// Handle nearby masjids button tap with location permission check
  void _handleNearbyMasjidsButtonTap(BuildContext context, WidgetRef ref) async {
    // Call optional callback
    onTap?.call();

    // For explicit user action, always try to request permission first
    // This ensures the system dialog is shown every time the user clicks
    await _requestLocationPermissionForUserAction(context, ref);
  }

  /// Request location permission directly for explicit user action
  /// This bypasses complex permission service logic and always shows system dialog
  ///
  /// Context7 MCP: Implements proper state synchronization after permission grant
  Future<void> _requestLocationPermissionForUserAction(BuildContext context, WidgetRef ref) async {
    try {
      // First check current permission status
      final currentPermission = await Geolocator.checkPermission();
      debugPrint('🔔 Current permission status: $currentPermission');

      // If already granted, show dialog directly
      if (currentPermission == LocationPermission.whileInUse || currentPermission == LocationPermission.always) {
        debugPrint('✅ Permission already granted, showing nearby masjids dialog');
        if (context.mounted) {
          unawaited(showDialog(context: context, builder: (context) => const NearbyMasjidsDialog()));
        }
        return;
      }

      // Check if permission is permanently denied
      if (currentPermission == LocationPermission.deniedForever) {
        debugPrint('⚠️ Permission permanently denied, opening app settings');
        // Show dialog to inform user and offer to open settings
        if (context.mounted) {
          await _showOpenSettingsDialog(context);
        }
        return;
      }

      // Permission not granted but can be requested, request it directly
      debugPrint('🔔 Requesting location permission for nearby masjids...');
      final permission = await Geolocator.requestPermission();
      debugPrint('🔔 Permission result: $permission');

      // Check if permission was granted
      if (permission == LocationPermission.whileInUse || permission == LocationPermission.always) {
        debugPrint('✅ Location permission granted, initializing location services...');

        // Context7 MCP: Proper state synchronization after permission grant
        await _initializeLocationServicesAfterPermissionGrant(ref);

        // Show the nearby masjids dialog after location services are ready
        if (context.mounted) {
          debugPrint('✅ Location services ready, showing nearby masjids dialog');
          unawaited(showDialog(context: context, builder: (context) => const NearbyMasjidsDialog()));
        }
      } else if (permission == LocationPermission.deniedForever) {
        // User selected "Don't ask again", offer to open settings
        debugPrint('⚠️ Permission permanently denied after request, offering settings');
        if (context.mounted) {
          await _showOpenSettingsDialog(context);
        }
      } else {
        // Permission denied but not permanently
        debugPrint('❌ Location permission denied by user for nearby masjids: $permission');
      }
    } on Exception catch (e) {
      debugPrint('Error requesting location permission: $e');
    }
  }

  /// Show dialog to open app settings for location permission
  Future<void> _showOpenSettingsDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(context.l10n?.locationPermissionRequired ?? 'Location Permission Required'),
          content: Text(
            context.l10n?.locationPermissionDenied ??
                'Location permission is required to show nearby masjids. Please enable location permission in app settings.',
          ),
          actions: <Widget>[
            TextButton(
              child: Text(context.l10n?.cancel ?? 'Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: Text(context.l10n?.openLocationSettings ?? 'Open Settings'),
              onPressed: () async {
                Navigator.of(context).pop();
                await Geolocator.openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  /// Initialize location services after permission is granted
  ///
  /// Context7 MCP: Implements proper state synchronization and location initialization
  /// following best practices for immediate location availability after permission grant
  Future<void> _initializeLocationServicesAfterPermissionGrant(WidgetRef ref) async {
    try {
      debugPrint('📍 NEARBY_BUTTON: Initializing location services after permission grant');

      // Step 1: Refresh location-related providers to trigger state update
      _refreshLocationProvidersAfterPermissionGrant(ref);

      // Step 2: Wait for provider state to propagate
      await Future.delayed(const Duration(milliseconds: 300));

      // Step 3: Trigger immediate location fetch using master location manager
      final locationManager = ref.read(masterLocationManagerProvider.notifier);
      debugPrint('📍 NEARBY_BUTTON: Requesting fresh location after permission grant');

      // Request fresh location to populate providers immediately
      final locationResult = await locationManager.getCurrentLocation(useCache: false);

      if (locationResult.isSuccess) {
        debugPrint('✅ NEARBY_BUTTON: Fresh location obtained after permission grant');

        // Step 4: Wait for location to propagate to all providers
        await Future.delayed(const Duration(milliseconds: 200));

        // Step 5: Verify location services are ready
        final isReady = ref.read(isLocationServiceReadyProvider);
        debugPrint('📍 NEARBY_BUTTON: Location services ready status: $isReady');

        if (!isReady) {
          debugPrint('⚠️ NEARBY_BUTTON: Location services not ready, waiting additional time');
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } else {
        debugPrint('❌ NEARBY_BUTTON: Failed to get fresh location: ${locationResult.errorOrNull}');
      }

      debugPrint('✅ NEARBY_BUTTON: Location services initialization completed');
    } on Exception catch (e) {
      debugPrint('❌ NEARBY_BUTTON: Error initializing location services: $e');
    }
  }

  /// Refresh location-related providers after permission is granted
  ///
  /// Implements Context7 MCP best practices for provider state management:
  /// - Invalidates relevant providers to trigger refresh
  /// - Ensures immediate location detection after permission grant
  void _refreshLocationProvidersAfterPermissionGrant(WidgetRef ref) {
    try {
      debugPrint('📍 NEARBY_BUTTON: Refreshing location providers after permission grant');

      // Context7 MCP: Invalidate unified location permission providers to refresh their state
      ref.invalidate(locationPermissionStreamProvider);

      // Context7 MCP: Invalidate unified location providers to refresh location state
      ref.invalidate(unifiedLocationManagerProvider);
      ref.invalidate(currentLocationProvider);

      debugPrint('✅ NEARBY_BUTTON: Location providers refreshed successfully');
    } on Exception catch (e) {
      debugPrint('❌ NEARBY_BUTTON: Error refreshing location providers: $e');
    }
  }
}

/// A positioned version of the nearby masjids button for use in stacks
class PositionedNearbyMasjidsButton extends StatelessWidget {
  /// Position from bottom
  final double? bottom;

  /// Position from top
  final double? top;

  /// Position from left
  final double? left;

  /// Position from right
  final double? right;

  /// Optional callback when button is tapped
  final VoidCallback? onTap;

  /// Constructor
  const PositionedNearbyMasjidsButton({super.key, this.bottom, this.top, this.left, this.right, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: bottom,
      top: top,
      left: left,
      right: right,
      child: NearbyMasjidsButton(onTap: onTap),
    );
  }
}
