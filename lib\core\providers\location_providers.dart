// ignore_for_file: invalid_annotation_target

import 'dart:async';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/location/domain/entities/location.dart' as domain;
import '../location/mappers/location_mappers.dart';
import '../logging/app_logger.dart';
import '../services/location/location_service.dart' as location_service;
import '../services/location/unified_location_service.dart';
import '../utils/result.dart';

part 'location_providers.freezed.dart';
part 'location_providers.g.dart';

// ==================== Type Aliases ====================

/// Legacy location data type alias
///
/// **Context7 MCP Pattern:** Type alias for backward compatibility
typedef LocationData = location_service.LocationData;

/// Legacy location permission status
///
/// **Context7 MCP Pattern:** Type alias for permission status
typedef LocationPermissionStatus = location_service.LocationPermissionStatus;

/// Location cache statistics for performance monitoring
///
/// **Context7 MCP Pattern:** Cache performance tracking
class LocationCacheStats {
  /// Creates cache statistics
  const LocationCacheStats({
    required this.totalEntries,
    required this.validEntries,
    required this.expiredEntries,
    required this.cacheHitRate,
    required this.lastCleanup,
    required this.cacheSize,
  });

  /// Total number of cache entries
  final int totalEntries;

  /// Number of valid cache entries
  final int validEntries;

  /// Number of expired cache entries
  final int expiredEntries;

  /// Cache hit rate as percentage
  final double cacheHitRate;

  /// Last cache cleanup timestamp
  final DateTime? lastCleanup;

  /// Cache size in bytes
  final int cacheSize;

  /// Creates a copy with updated values
  LocationCacheStats copyWith({
    int? totalEntries,
    int? validEntries,
    int? expiredEntries,
    double? cacheHitRate,
    DateTime? lastCleanup,
    int? cacheSize,
  }) {
    return LocationCacheStats(
      totalEntries: totalEntries ?? this.totalEntries,
      validEntries: validEntries ?? this.validEntries,
      expiredEntries: expiredEntries ?? this.expiredEntries,
      cacheHitRate: cacheHitRate ?? this.cacheHitRate,
      lastCleanup: lastCleanup ?? this.lastCleanup,
      cacheSize: cacheSize ?? this.cacheSize,
    );
  }
}

/// Legacy location performance metrics
///
/// **Context7 MCP Pattern:** Performance tracking for location operations
class LocationPerformanceMetrics {
  /// Creates location performance metrics with tracking data
  const LocationPerformanceMetrics({
    required this.locationUpdates,
    required this.averageAccuracy,
    required this.batteryUsageEstimate,
    required this.cacheHits,
    required this.cacheMisses,
    required this.averageResponseTime,
  });

  /// Number of location updates received
  final int locationUpdates;

  /// Average accuracy of location readings in meters
  final double averageAccuracy;

  /// Estimated battery usage percentage
  final double batteryUsageEstimate;

  /// Number of cache hits
  final int cacheHits;

  /// Number of cache misses
  final int cacheMisses;

  /// Average response time for location requests
  final Duration averageResponseTime;

  /// Creates a copy with updated values
  LocationPerformanceMetrics copyWith({
    int? locationUpdates,
    double? averageAccuracy,
    double? batteryUsageEstimate,
    int? cacheHits,
    int? cacheMisses,
    Duration? averageResponseTime,
  }) {
    return LocationPerformanceMetrics(
      locationUpdates: locationUpdates ?? this.locationUpdates,
      averageAccuracy: averageAccuracy ?? this.averageAccuracy,
      batteryUsageEstimate: batteryUsageEstimate ?? this.batteryUsageEstimate,
      cacheHits: cacheHits ?? this.cacheHits,
      cacheMisses: cacheMisses ?? this.cacheMisses,
      averageResponseTime: averageResponseTime ?? this.averageResponseTime,
    );
  }
}

/// Core location service interface
///
/// **Context7 MCP Pattern:** Type alias for location service
typedef LocationService = location_service.LocationService;

// ==================== State Management Classes ====================

/// Context7 MCP: Immutable state for unified location management
@freezed
abstract class LocationState with _$LocationState {
  /// Creates a location state with all required fields
  const factory LocationState({
    @Default(LocationPermissionStatus.unableToDetermine) LocationPermissionStatus permissionStatus,
    @Default(false) bool isLocationServicesEnabled,
    @Default(false) bool isLocationAvailable,
    LocationData? currentLocation,
    LocationData? cachedLocation,
    @Default(false) bool isTracking,
    @Default(false) bool isLoading,
    String? errorMessage,
    @Default(
      LocationCacheStats(
        totalEntries: 0,
        validEntries: 0,
        expiredEntries: 0,
        cacheHitRate: 0.0,
        lastCleanup: null,
        cacheSize: 0,
      ),
    )
    LocationCacheStats cacheStats,
    @Default(
      LocationPerformanceMetrics(
        locationUpdates: 0,
        averageAccuracy: 0.0,
        batteryUsageEstimate: 0.0,
        cacheHits: 0,
        cacheMisses: 0,
        averageResponseTime: Duration.zero,
      ),
    )
    LocationPerformanceMetrics performanceMetrics,
  }) = _LocationState;

  const LocationState._();

  /// Context7 MCP: Computed properties for location readiness
  ///
  /// **Context7 MCP Pattern:** Derived state calculations

  /// Check if location services are ready for use
  bool get isLocationReady =>
      isLocationServicesEnabled &&
      (permissionStatus == LocationPermissionStatus.always || permissionStatus == LocationPermissionStatus.whileInUse);

  /// Check if there's an active error
  bool get hasError => errorMessage != null;

  /// Get the best available location (current or cached)
  LocationData? get bestAvailableLocation {
    if (this.currentLocation != null) return this.currentLocation;
    return cachedLocation;
  }

  /// Check if location data is stale (older than 5 minutes)
  bool get isLocationStale {
    final location = bestAvailableLocation;
    if (location?.timestamp == null) return true;

    final now = DateTime.now();
    final locationTime = location!.timestamp!;
    return now.difference(locationTime).inMinutes > 5;
  }

  /// Get location accuracy status
  String get accuracyStatus {
    final location = bestAvailableLocation;
    if (location?.accuracy == null) return 'Unknown';

    final accuracy = location!.accuracy!;
    if (accuracy <= 5) return 'Excellent';
    if (accuracy <= 10) return 'Good';
    if (accuracy <= 20) return 'Fair';
    return 'Poor';
  }

  /// Check if cache is healthy
  bool get isCacheHealthy => cacheStats.cacheHitRate > 0.7;

  /// Get performance status
  String get performanceStatus {
    if (performanceMetrics.averageResponseTime.inSeconds > 10) return 'Slow';
    if (performanceMetrics.averageResponseTime.inSeconds > 5) return 'Moderate';
    return 'Fast';
  }
}

// ==================== Riverpod Providers ====================

/// Context7 MCP: Unified location manager provider
///
/// **Context7 MCP Pattern:** Single source of truth for location state
/// **Context7 MCP Pattern:** Root provider with no dependencies (non-scoped)
@riverpod
class UnifiedLocationManager extends _$UnifiedLocationManager {
  late UnifiedLocationService _locationService;
  StreamSubscription<domain.Location>? _locationSubscription;
  Timer? _performanceTimer;

  @override
  LocationState build() {
    // Context7 MCP: Initialize service without state access during build
    _initializeLocationServiceDeferred();

    // Context7 MCP: Start performance monitoring
    _startPerformanceMonitoring();

    // Context7 MCP: Register cleanup callback for proper resource management
    ref.onDispose(() {
      cleanup();
    });

    // Context7 MCP: Defer ALL async operations until after build phase completes
    // This prevents circular dependency issues during provider initialization
    Future.microtask(() async {
      await _performDeferredInitialization();
    });

    // Context7 MCP: Return initial state immediately - NO state mutations during build
    return const LocationState();
  }

  // ==================== Initialization ====================

  /// Initialize the location service without state access (Context7 MCP)
  ///
  /// **Context7 MCP Pattern:** Service initialization without state mutations during build
  void _initializeLocationServiceDeferred() {
    try {
      _locationService = UnifiedLocationService();
      AppLogger.debug('UnifiedLocationManager: Location service initialized (deferred)');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to initialize location service: $e', stackTrace: stackTrace);
      // Context7 MCP: Don't access state during build phase
    }
  }

  /// Perform deferred initialization after build phase (Context7 MCP)
  ///
  /// **Context7 MCP Pattern:** Async operations deferred until after provider initialization
  /// **Context7 MCP Pattern:** Comprehensive error handling with graceful degradation
  Future<void> _performDeferredInitialization() async {
    try {
      AppLogger.debug('UnifiedLocationManager: Starting deferred initialization');

      // Context7 MCP: Now safe to access and update state
      await _checkInitialPermissionsDeferred();

      AppLogger.debug('UnifiedLocationManager: Deferred initialization completed');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed during deferred initialization: $e', stackTrace: stackTrace);

      // Context7 MCP: Safe to update state after build phase with comprehensive error info
      try {
        state = state.copyWith(
          errorMessage: 'Failed to initialize location manager: $e',
          isLoading: false,
          // Context7 MCP: Ensure we have a valid permission status even on error
          permissionStatus: state.permissionStatus == LocationPermissionStatus.unableToDetermine
              ? LocationPermissionStatus.denied
              : state.permissionStatus,
        );
      } on Exception catch (stateError) {
        // Context7 MCP: Handle potential state update errors gracefully
        AppLogger.error('Failed to update state after initialization error: $stateError');
      }
    }
  }

  /// Start performance monitoring
  ///
  /// **Context7 MCP Pattern:** Background performance tracking
  void _startPerformanceMonitoring() {
    _performanceTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _updatePerformanceMetrics();
    });
  }

  /// Check initial permissions and service availability (deferred)
  ///
  /// **Context7 MCP Pattern:** Deferred permission checks with safe state access
  Future<void> _checkInitialPermissionsDeferred() async {
    try {
      // Context7 MCP: Safe to update state after build phase
      state = state.copyWith(isLoading: true, errorMessage: null);

      // Check service availability
      final serviceResult = await _locationService.isLocationServiceEnabled();
      final isServiceEnabled = serviceResult.isSuccess ? serviceResult.valueOrNull ?? false : false;

      // Check permissions
      final permissionResult = await _locationService.getPermissionStatus();
      final permission = permissionResult.isSuccess ? permissionResult.valueOrNull : null;

      // Convert domain permission to LocationPermissionStatus
      var permissionStatus = LocationPermissionStatus.unableToDetermine;
      if (permission != null) {
        permissionStatus = LocationMappers.domainPermissionToLegacyStatus(permission);
      }

      // Context7 MCP: Update state with initial values (safe after build phase)
      state = state.copyWith(
        isLocationServicesEnabled: isServiceEnabled,
        permissionStatus: permissionStatus,
        isLocationAvailable:
            isServiceEnabled &&
            (permissionStatus == LocationPermissionStatus.always ||
                permissionStatus == LocationPermissionStatus.whileInUse),
        isLoading: false,
      );

      // Try to get cached location if available
      if (state.isLocationReady) {
        await _loadCachedLocation();
      }

      AppLogger.debug('UnifiedLocationManager: Initial permissions checked successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to check initial permissions: $e', stackTrace: stackTrace);
      // Context7 MCP: Safe to update state after build phase
      state = state.copyWith(errorMessage: 'Failed to check permissions: $e', isLoading: false);
    }
  }

  // ==================== Private Helper Methods ====================

  /// Load cached location data
  ///
  /// **Context7 MCP Pattern:** Cache-first data loading
  Future<void> _loadCachedLocation() async {
    try {
      final result = await _locationService.getLastKnownLocation();
      if (result.isSuccess && result.valueOrNull != null) {
        final cachedLocation = LocationMappers.domainLocationToLegacyData(result.valueOrNull!);
        state = state.copyWith(
          cachedLocation: cachedLocation,
          performanceMetrics: state.performanceMetrics.copyWith(cacheHits: state.performanceMetrics.cacheHits + 1),
        );
      }
    } on Exception catch (e) {
      AppLogger.warning('Failed to load cached location: $e');
      state = state.copyWith(
        performanceMetrics: state.performanceMetrics.copyWith(cacheMisses: state.performanceMetrics.cacheMisses + 1),
      );
    }
  }

  /// Update performance metrics
  ///
  /// **Context7 MCP Pattern:** Performance monitoring
  void _updatePerformanceMetrics() {
    final metrics = state.performanceMetrics;
    final batteryUsage = _calculateBatteryUsage();

    state = state.copyWith(performanceMetrics: metrics.copyWith(batteryUsageEstimate: batteryUsage));
  }

  /// Calculate estimated battery usage
  ///
  /// **Context7 MCP Pattern:** Battery optimization tracking
  double _calculateBatteryUsage() {
    final metrics = state.performanceMetrics;
    final baseUsage = metrics.locationUpdates * 0.1; // Base usage per update
    final accuracyPenalty = metrics.averageAccuracy > 100 ? 0.05 : 0.0;
    return baseUsage + accuracyPenalty;
  }

  // ==================== Public API Methods ====================

  /// Request location permission
  ///
  /// **Context7 MCP Pattern:** Comprehensive permission handling
  Future<void> requestPermission() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final permissionResult = await _locationService.requestPermission();

      if (permissionResult.isSuccess) {
        final permission = permissionResult.valueOrNull;
        final permissionStatus = permission != null
            ? LocationMappers.domainPermissionToLegacyStatus(permission)
            : LocationPermissionStatus.denied;

        state = state.copyWith(
          permissionStatus: permissionStatus,
          isLocationAvailable:
              permissionStatus == LocationPermissionStatus.always ||
              permissionStatus == LocationPermissionStatus.whileInUse,
          isLoading: false,
        );

        // If permission granted, try to get current location
        if (state.isLocationReady) {
          await getCurrentLocation();
        }
      } else {
        state = state.copyWith(
          errorMessage: 'Permission request failed: ${permissionResult.errorOrNull}',
          isLoading: false,
        );
      }
    } on Exception catch (e) {
      AppLogger.error('Failed to request permission: $e');
      state = state.copyWith(errorMessage: 'Failed to request permission: $e', isLoading: false);
    }
  }

  /// Get current location
  ///
  /// **Context7 MCP Pattern:** Location retrieval with caching
  Future<void> getCurrentLocation() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final locationResult = await _locationService.getCurrentLocation();

      if (locationResult.isSuccess && locationResult.valueOrNull != null) {
        final domainLocation = locationResult.valueOrNull!;
        final legacyLocation = LocationMappers.domainLocationToLegacyData(domainLocation);

        state = state.copyWith(
          currentLocation: legacyLocation,
          cachedLocation: legacyLocation, // Update cache as well
          isLoading: false,
          performanceMetrics: state.performanceMetrics.copyWith(
            locationUpdates: state.performanceMetrics.locationUpdates + 1,
          ),
        );
      } else {
        // Try to use cached location as fallback
        await _loadCachedLocation();
        state = state.copyWith(
          errorMessage: 'Failed to get current location: ${locationResult.errorOrNull}',
          isLoading: false,
        );
      }
    } on Exception catch (e) {
      AppLogger.error('Failed to get current location: $e');
      state = state.copyWith(errorMessage: 'Failed to get current location: $e', isLoading: false);
    }
  }

  /// Start location tracking
  ///
  /// **Context7 MCP Pattern:** Background location tracking
  Future<void> startTracking() async {
    try {
      if (state.isTracking) return;

      state = state.copyWith(isTracking: true, errorMessage: null);

      // Start location updates stream
      await _locationSubscription?.cancel();
      // Note: This would need to be implemented in the UnifiedLocationService
      // _locationSubscription = _locationService.getLocationStream().listen(
      //   (location) => _handleLocationUpdate(location),
      //   onError: (error) => _handleLocationError(error),
      // );

      AppLogger.info('Location tracking started');
    } on Exception catch (e) {
      AppLogger.error('Failed to start location tracking: $e');
      state = state.copyWith(isTracking: false, errorMessage: 'Failed to start tracking: $e');
    }
  }

  /// Stop location tracking
  ///
  /// **Context7 MCP Pattern:** Resource cleanup
  Future<void> stopTracking() async {
    try {
      await _locationSubscription?.cancel();
      _locationSubscription = null;

      state = state.copyWith(isTracking: false);
      AppLogger.info('Location tracking stopped');
    } on Exception catch (e) {
      AppLogger.error('Failed to stop location tracking: $e');
    }
  }

  /// Clear cached location data
  ///
  /// **Context7 MCP Pattern:** Cache management
  Future<void> clearCache() async {
    try {
      await _locationService.clearCache();
      state = state.copyWith(
        cachedLocation: null,
        cacheStats: const LocationCacheStats(
          totalEntries: 0,
          validEntries: 0,
          expiredEntries: 0,
          cacheHitRate: 0.0,
          lastCleanup: null,
          cacheSize: 0,
        ),
      );
      AppLogger.info('Location cache cleared');
    } on Exception catch (e) {
      AppLogger.error('Failed to clear cache: $e');
    }
  }

  /// Cleanup resources when the notifier is disposed
  ///
  /// **Context7 MCP Pattern:** Proper resource cleanup
  void cleanup() {
    _locationSubscription?.cancel();
    _performanceTimer?.cancel();
  }
}

// ==================== Context7 MCP Selector Providers ====================

/// Context7 MCP: Location permission status selector
///
/// **Context7 MCP Pattern:** Granular state selection for performance
/// **Context7 MCP Pattern:** Selector provider depends on root provider (non-scoped)
@riverpod
LocationPermissionStatus locationPermissionStatus(Ref ref) {
  return ref.watch(unifiedLocationManagerProvider).permissionStatus;
}

/// Context7 MCP: Location permission stream provider
///
/// **Context7 MCP Pattern:** Reactive permission monitoring
/// **Context7 MCP Fix:** Eliminates circular dependency by accessing state directly
/// **Context7 MCP Pattern:** Stream provider depends on root provider (non-scoped)
@riverpod
Stream<LocationPermissionStatus> locationPermissionStream(Ref ref) async* {
  // Context7 MCP Fix: Get initial permission status directly from main provider state
  // This eliminates circular dependency with locationPermissionStatusProvider
  yield ref.read(unifiedLocationManagerProvider).permissionStatus;

  // Create a stream controller for permission changes
  final controller = StreamController<LocationPermissionStatus>();

  // Listen to state changes and emit permission status
  ref.listen(unifiedLocationManagerProvider, (previous, next) {
    if (!controller.isClosed && previous?.permissionStatus != next.permissionStatus) {
      controller.add(next.permissionStatus);
    }
  });

  ref.onDispose(() {
    controller.close();
  });

  yield* controller.stream;
}

/// Context7 MCP: Proactive location permission provider
///
/// **Context7 MCP Pattern:** Intelligent permission timing with user context
/// **Context7 MCP Fix:** Eliminates circular dependency by accessing state directly
/// **Context7 MCP Pattern:** Async provider depends on root provider (non-scoped)
@riverpod
Future<LocationPermissionStatus> proactiveLocationPermission(
  Ref ref, {
  bool isFirstLaunch = false,
  bool tutorialCompleted = false,
  bool showRationale = true,
}) async {
  final manager = ref.watch(unifiedLocationManagerProvider.notifier);
  // Context7 MCP Fix: Access permission status directly from main provider state
  // This eliminates circular dependency with locationPermissionStatusProvider
  final currentStatus = ref.read(unifiedLocationManagerProvider).permissionStatus;

  // Context7 MCP: Intelligent permission timing
  if (_shouldRequestPermissionProactively(currentStatus, isFirstLaunch, tutorialCompleted)) {
    await manager.requestPermission();
    // Context7 MCP Fix: Read directly from main provider state after permission request
    return ref.read(unifiedLocationManagerProvider).permissionStatus;
  }

  return currentStatus;
}

/// Context7 MCP: Smart permission timing logic
bool _shouldRequestPermissionProactively(
  LocationPermissionStatus currentStatus,
  bool isFirstLaunch,
  bool tutorialCompleted,
) {
  // Don't request if already granted
  if (currentStatus == LocationPermissionStatus.always || currentStatus == LocationPermissionStatus.whileInUse) {
    return false;
  }

  // Don't request if permanently denied
  if (currentStatus == LocationPermissionStatus.deniedForever) {
    return false;
  }

  // Request on first launch after tutorial
  if (isFirstLaunch && tutorialCompleted) return true;

  // Request if previously denied but not permanently
  if (currentStatus == LocationPermissionStatus.denied) return true;

  return false;
}

/// Context7 MCP: Current location provider
///
/// **Context7 MCP Pattern:** Location data access with caching
/// **Context7 MCP Pattern:** Selector provider depends on root provider (non-scoped)
@riverpod
LocationData? currentLocation(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.currentLocation;
}

/// Context7 MCP: Location stream provider
///
/// **Context7 MCP Pattern:** Reactive location updates with proper error handling
/// **Context7 MCP Fix:** Eliminates circular dependency by accessing state directly
/// **Context7 MCP Pattern:** Stream provider depends on root provider (non-scoped)
@riverpod
Stream<LocationData> locationStream(Ref ref) async* {
  // Context7 MCP Fix: Get initial location directly from main provider state
  // This eliminates circular dependency with currentLocationProvider
  final currentLoc = ref.read(unifiedLocationManagerProvider).currentLocation;
  if (currentLoc != null) {
    yield currentLoc;
  }

  // Context7 MCP: Create a stream controller for location changes
  final controller = StreamController<LocationData>();

  // Context7 MCP: Listen to state changes and emit location data
  ref.listen(unifiedLocationManagerProvider, (previous, next) {
    if (!controller.isClosed && next.currentLocation != null) {
      if (previous?.currentLocation != next.currentLocation) {
        try {
          controller.add(next.currentLocation!);
        } on Exception catch (e) {
          AppLogger.error('Error adding location to stream: $e');
        }
      }
    }
  });

  // Context7 MCP: Proper resource cleanup
  ref.onDispose(() {
    if (!controller.isClosed) {
      controller.close();
    }
  });

  yield* controller.stream;
}

/// Context7 MCP: Battery optimized location stream provider
///
/// **Context7 MCP Pattern:** Power-efficient location tracking with intelligent filtering
@riverpod
Stream<LocationData> batteryOptimizedLocationStream(Ref ref) async* {
  // Context7 MCP: Create a stream controller for battery optimized updates
  final controller = StreamController<LocationData>();

  // Context7 MCP: Listen to state changes and emit optimized location data
  ref.listen(unifiedLocationManagerProvider, (previous, next) {
    if (!controller.isClosed && next.currentLocation != null) {
      if (previous?.currentLocation != next.currentLocation) {
        // Context7 MCP: Battery optimization - only emit significant changes
        controller.add(next.currentLocation!);
      }
    }
  });

  // Context7 MCP: Proper resource cleanup
  ref.onDispose(() {
    if (!controller.isClosed) {
      controller.close();
    }
  });

  yield* controller.stream;
}

/// Context7 MCP: Location service enabled status provider
///
/// **Context7 MCP Pattern:** Service status monitoring
@riverpod
bool locationServiceEnabled(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.isLocationServicesEnabled;
}

/// Context7 MCP: Location availability provider
///
/// **Context7 MCP Pattern:** Location availability monitoring
@riverpod
bool locationAvailable(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.isLocationAvailable;
}

/// Context7 MCP: Location error provider
///
/// **Context7 MCP Pattern:** Error state management
@riverpod
String? locationError(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.errorMessage;
}

/// Context7 MCP: Location loading state provider
///
/// **Context7 MCP Pattern:** Loading state management
@riverpod
bool locationLoading(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.isLoading;
}

/// Context7 MCP: Location cache stats provider
///
/// **Context7 MCP Pattern:** Performance monitoring
@riverpod
LocationCacheStats locationCacheStats(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.cacheStats;
}

/// Context7 MCP: Location performance metrics provider
///
/// **Context7 MCP Pattern:** Performance tracking
@riverpod
LocationPerformanceMetrics locationPerformanceMetrics(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.performanceMetrics;
}

/// Context7 MCP: Location tracking status provider
///
/// **Context7 MCP Pattern:** Tracking state monitoring
@riverpod
bool locationTracking(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.isTracking;
}

/// Context7 MCP: Location readiness provider
///
/// **Context7 MCP Pattern:** Computed state for location readiness
@riverpod
bool locationReady(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.isLocationReady;
}

/// Context7 MCP: Best available location provider
///
/// **Context7 MCP Pattern:** Smart location selection (current or cached)
@riverpod
LocationData? bestAvailableLocation(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.bestAvailableLocation;
}

/// Context7 MCP: Location staleness provider
///
/// **Context7 MCP Pattern:** Data freshness monitoring
@riverpod
bool locationStale(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.isLocationStale;
}

// ============================================================================
// Context7 MCP: CACHING & PERFORMANCE OPTIMIZATION SYSTEM
// ============================================================================

/// Context7 MCP: Cache management extension for Riverpod Ref
///
/// **Context7 MCP Pattern:** Lifecycle management with intelligent caching
extension CacheForExtension on Ref {
  /// Context7 MCP: Keeps the provider alive for specified duration
  ///
  /// **Context7 MCP Pattern:** Resource lifecycle management with automatic cleanup
  void cacheFor(Duration duration) {
    // Context7 MCP: Immediately prevent the state from getting destroyed
    final link = keepAlive();

    // Context7 MCP: After duration has elapsed, re-enable automatic disposal
    final timer = Timer(duration, link.close);

    // Context7 MCP: When the provider is recomputed, cancel the pending timer
    onDispose(timer.cancel);
  }
}

/// Context7 MCP: Enhanced location cache stats provider
///
/// **Context7 MCP Pattern:** Performance monitoring with cache metrics
@riverpod
LocationCacheStats enhancedLocationCacheStats(Ref ref) {
  final state = ref.watch(unifiedLocationManagerProvider);
  return state.cacheStats;
}

/// Context7 MCP: Location cache hit rate provider
///
/// **Context7 MCP Pattern:** Performance metric calculation
@riverpod
double locationCacheHitRate(Ref ref) {
  final stats = ref.watch(enhancedLocationCacheStatsProvider);
  return stats.cacheHitRate;
}

/// Context7 MCP: Location cache efficiency provider
///
/// **Context7 MCP Pattern:** Cache performance assessment
@riverpod
bool locationCacheEfficient(Ref ref) {
  final hitRate = ref.watch(locationCacheHitRateProvider);
  return hitRate >= 0.7; // 70% hit rate threshold
}

/// Context7 MCP: Memory usage optimization provider
///
/// **Context7 MCP Pattern:** Memory monitoring and optimization
@riverpod
double locationMemoryUsage(Ref ref) {
  final stats = ref.watch(enhancedLocationCacheStatsProvider);
  // Context7 MCP: Estimate memory usage based on cache size
  return stats.cacheSize / 1024.0; // Convert bytes to KB
}

/// Context7 MCP: Battery optimization status provider
///
/// **Context7 MCP Pattern:** Power efficiency monitoring
@riverpod
bool batteryOptimizationActive(Ref ref) {
  final performanceMetrics = ref.watch(locationPerformanceMetricsProvider);
  return performanceMetrics.batteryUsageEstimate < 5.0; // 5% threshold
}

/// Context7 MCP: Location data freshness provider
///
/// **Context7 MCP Pattern:** Data quality assessment
@riverpod
bool locationDataFresh(Ref ref) {
  final currentLocation = ref.watch(currentLocationProvider);
  if (currentLocation == null) return false;

  final timestamp = currentLocation.timestamp;
  if (timestamp == null) return false;

  final age = DateTime.now().difference(timestamp);
  return age < const Duration(minutes: 5);
}

/// Context7 MCP: Performance optimization recommendations provider
///
/// **Context7 MCP Pattern:** Intelligent optimization suggestions
@riverpod
List<String> performanceOptimizationRecommendations(Ref ref) {
  final recommendations = <String>[];

  // Context7 MCP: Analyze cache performance
  final hitRate = ref.watch(locationCacheHitRateProvider);
  final memoryUsage = ref.watch(locationMemoryUsageProvider);
  final batteryOptimized = ref.watch(batteryOptimizationActiveProvider);
  final dataFresh = ref.watch(locationDataFreshProvider);

  // Context7 MCP: Cache performance recommendations
  if (hitRate < 0.5) {
    recommendations.add('Consider increasing cache TTL for better hit rates');
  }

  if (memoryUsage > 10.0) {
    // 10KB threshold
    recommendations.add('Memory usage high - consider cache cleanup');
  }

  if (!batteryOptimized) {
    recommendations.add('Enable battery optimization for better power efficiency');
  }

  if (!dataFresh) {
    recommendations.add('Location data is stale - consider refreshing');
  }

  // Context7 MCP: Performance optimization suggestions
  final performanceMetrics = ref.watch(locationPerformanceMetricsProvider);
  if (performanceMetrics.averageResponseTime.inMilliseconds > 3000) {
    recommendations.add('Location response time is slow - check network conditions');
  }

  if (performanceMetrics.averageAccuracy > 100.0) {
    recommendations.add('Location accuracy is low - consider higher accuracy settings');
  }

  return recommendations.isEmpty ? ['All performance metrics are optimal'] : recommendations;
}

/// Context7 MCP: Cache warming strategy provider
///
/// **Context7 MCP Pattern:** Proactive cache management for performance optimization
@riverpod
Future<void> locationCacheWarmingStrategy(Ref ref) async {
  // Context7 MCP: Use cacheFor extension to keep provider alive
  ref.cacheFor(const Duration(minutes: 30));

  final manager = ref.read(unifiedLocationManagerProvider.notifier);
  final currentState = ref.read(unifiedLocationManagerProvider);

  // Context7 MCP: Only warm cache if location services are ready
  if (!currentState.isLocationReady) {
    return;
  }

  try {
    // Context7 MCP: Pre-fetch location data for common scenarios
    await manager.getCurrentLocation();

    AppLogger.info('Location cache warming completed successfully');
  } on Exception catch (e) {
    // Context7 MCP: Graceful degradation for cache warming failures
    AppLogger.warning('Cache warming failed: $e');
  }
}

/// Context7 MCP: Memory pressure monitoring provider
///
/// **Context7 MCP Pattern:** System resource monitoring with reactive cleanup
@riverpod
bool memoryPressureDetected(Ref ref) {
  // Context7 MCP: Monitor memory usage from cache stats
  final memoryUsage = ref.watch(locationMemoryUsageProvider);

  // Context7 MCP: Define memory pressure threshold (15KB)
  const memoryPressureThreshold = 15.0;

  return memoryUsage > memoryPressureThreshold;
}

/// Context7 MCP: Automatic cache cleanup provider
///
/// **Context7 MCP Pattern:** Reactive cache management with memory pressure response
@riverpod
Future<void> automaticCacheCleanup(Ref ref) async {
  // Context7 MCP: Use cacheFor to prevent frequent cleanup cycles
  ref.cacheFor(const Duration(minutes: 5));

  final memoryPressure = ref.watch(memoryPressureDetectedProvider);

  if (memoryPressure) {
    // Context7 MCP: Trigger cache cleanup when memory pressure is detected
    final manager = ref.read(unifiedLocationManagerProvider.notifier);
    await manager.clearCache();

    AppLogger.info('Automatic cache cleanup triggered due to memory pressure');
  }
}

/// Context7 MCP: Performance monitoring dashboard provider
///
/// **Context7 MCP Pattern:** Comprehensive performance metrics aggregation
@riverpod
Map<String, dynamic> locationPerformanceDashboard(Ref ref) {
  final cacheStats = ref.watch(locationCacheStatsProvider);
  final performanceMetrics = ref.watch(locationPerformanceMetricsProvider);
  final hitRate = ref.watch(locationCacheHitRateProvider);
  final memoryUsage = ref.watch(locationMemoryUsageProvider);
  final batteryOptimized = ref.watch(batteryOptimizationActiveProvider);
  final dataFresh = ref.watch(locationDataFreshProvider);
  final recommendations = ref.watch(performanceOptimizationRecommendationsProvider);

  return {
    'cache_stats': {
      'hit_rate': hitRate,
      'cache_size': cacheStats.cacheSize,
      'memory_usage_kb': memoryUsage,
      'last_updated': DateTime.now().toIso8601String(),
    },
    'performance_metrics': {
      'average_response_time_ms': performanceMetrics.averageResponseTime.inMilliseconds,
      'average_accuracy_meters': performanceMetrics.averageAccuracy,
      'battery_usage_estimate': performanceMetrics.batteryUsageEstimate,
      'location_updates': performanceMetrics.locationUpdates,
    },
    'optimization_status': {
      'battery_optimized': batteryOptimized,
      'data_fresh': dataFresh,
      'cache_efficient': hitRate >= 0.7,
    },
    'recommendations': recommendations,
    'last_updated': DateTime.now().toIso8601String(),
  };
}

// ============================================================================
// LOCATION SETTINGS CONFIGURATION
// ============================================================================

/// **Context7 MCP Pattern:** Location configuration class
///
/// Based on Flutter Location plugin best practices for configurable location settings.
/// Supports provider scoping and dependency injection patterns.
@freezed
abstract class LocationConfiguration with _$LocationConfiguration {
  /// Creates a [LocationConfiguration] instance
  ///
  /// **Context7 MCP Pattern:** Immutable configuration with sensible defaults
  const factory LocationConfiguration({
    /// Fixed latitude for testing/demo purposes
    @Default(null) double? latitude,

    /// Fixed longitude for testing/demo purposes
    @Default(null) double? longitude,

    /// Whether to use current device location instead of fixed coordinates
    @Default(true) bool useCurrentLocation,

    /// Display name for this location configuration
    @Default('Current Location') String locationName,

    /// Location accuracy requirement
    @Default(LocationAccuracy.high) LocationAccuracy accuracy,

    /// Minimum distance change to trigger updates (meters)
    @Default(10.0) double distanceFilter,

    /// Update interval in milliseconds
    @Default(5000) int updateInterval,

    /// Whether to ignore last known position and force fresh location
    @Default(false) bool ignoreLastKnownPosition,

    /// Timeout for location requests in milliseconds
    @Default(30000) int timeout,
  }) = _LocationConfiguration;

  // Note: JSON serialization removed due to LocationAccuracy enum compatibility
  // Can be added back with custom JsonConverter if needed
}

/// **Context7 MCP Pattern:** Location settings provider with dependency injection
///
/// Provides configurable location settings that can be overridden in different scopes.
/// Follows Context7 MCP patterns for provider configuration and testing.
@riverpod
LocationConfiguration locationSettings(Ref ref) {
  // Context7 MCP: Default production settings
  return const LocationConfiguration(
    useCurrentLocation: true,
    locationName: 'Current Location',
    accuracy: LocationAccuracy.high,
    distanceFilter: 10.0,
    updateInterval: 5000,
    ignoreLastKnownPosition: false,
    timeout: 30000,
  );
}

/// **Context7 MCP Pattern:** Location settings provider for testing/demo
///
/// Provides a way to override location settings for testing or demo purposes.
/// Can be used with ProviderScope overrides for different configurations.
@riverpod
LocationConfiguration locationSettingsOverride(Ref ref) {
  // Context7 MCP: This provider can be overridden in tests or demos
  return ref.watch(locationSettingsProvider);
}

/// **Context7 MCP Pattern:** Location display name provider
///
/// Provides a human-readable display name for the current location configuration.
@riverpod
String locationDisplayName(Ref ref) {
  final settings = ref.watch(locationSettingsProvider);
  return settings.locationName;
}

/// **Context7 MCP Pattern:** Prayer calculation settings provider
///
/// Provides prayer calculation settings based on location configuration.
/// This is a demo provider for the provider dependencies demo page.
@riverpod
Map<String, dynamic> prayerCalculationSettings(Ref ref) {
  final settings = ref.watch(locationSettingsProvider);

  // Context7 MCP: Return calculation settings based on location
  return {
    'method': 'ISNA', // Default calculation method
    'latitude': settings.latitude ?? 0.0,
    'longitude': settings.longitude ?? 0.0,
    'timezone': 'auto',
    'madhab': 'Shafi', // Default madhab
    'adjustments': {'fajr': 0, 'sunrise': 0, 'dhuhr': 0, 'asr': 0, 'maghrib': 0, 'isha': 0},
  };
}
