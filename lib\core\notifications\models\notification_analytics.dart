import 'package:flutter/foundation.dart';

import 'notification_channel.dart';

/// Notification Delivery Status
///
/// Enumeration of possible notification delivery statuses for analytics tracking.
enum NotificationDeliveryStatus {
  /// Notification was successfully delivered
  delivered,

  /// Notification delivery failed
  failed,

  /// Notification is pending delivery
  pending,

  /// Notification was cancelled before delivery
  cancelled,
}

/// Notification Interaction Type
///
/// Enumeration of user interaction types with notifications for engagement tracking.
enum NotificationInteractionType {
  /// User opened/viewed the notification
  opened,

  /// User clicked on the notification
  clicked,

  /// User dismissed the notification
  dismissed,

  /// User performed a specific action
  actionPerformed,

  /// User snoozed the notification
  snoozed,

  /// User disabled notifications for this channel
  channelDisabled,
}

/// Base Notification Analytics Event
///
/// Abstract base class for all notification analytics events following Context7 MCP best practices.
@immutable
abstract class NotificationAnalyticsEvent {
  /// Unique identifier for the event
  final String id;

  /// Timestamp when the event occurred
  final DateTime timestamp;

  /// Additional metadata for the event
  final Map<String, dynamic> metadata;

  /// Create a notification analytics event
  const NotificationAnalyticsEvent({required this.id, required this.timestamp, this.metadata = const {}});

  /// Convert to JSON representation
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationAnalyticsEvent && other.id == id && other.timestamp == timestamp;
    // Note: Simplified comparison - metadata comparison removed for now
  }

  @override
  int get hashCode => Object.hash(id, timestamp, metadata);
}

/// Notification Delivery Event
///
/// Analytics event for tracking notification delivery status and timing.
@immutable
class NotificationDeliveryEvent extends NotificationAnalyticsEvent {
  /// ID of the notification that was delivered
  final String notificationId;

  /// Channel through which the notification was delivered
  final NotificationChannelKey channelKey;

  /// Time when the delivery was attempted
  final DateTime deliveryTime;

  /// Status of the delivery attempt
  final NotificationDeliveryStatus deliveryStatus;

  /// Reason for delivery failure (if applicable)
  final String? failureReason;

  /// Create a notification delivery event
  const NotificationDeliveryEvent({
    required super.id,
    required this.notificationId,
    required this.channelKey,
    required this.deliveryTime,
    required this.deliveryStatus,
    this.failureReason,
    super.metadata = const {},
    required super.timestamp,
  });

  /// Create a copy with updated properties
  NotificationDeliveryEvent copyWith({
    String? id,
    String? notificationId,
    NotificationChannelKey? channelKey,
    DateTime? deliveryTime,
    NotificationDeliveryStatus? deliveryStatus,
    String? failureReason,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
  }) {
    return NotificationDeliveryEvent(
      id: id ?? this.id,
      notificationId: notificationId ?? this.notificationId,
      channelKey: channelKey ?? this.channelKey,
      deliveryTime: deliveryTime ?? this.deliveryTime,
      deliveryStatus: deliveryStatus ?? this.deliveryStatus,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'notificationId': notificationId,
      'channelKey': channelKey.name,
      'deliveryTime': deliveryTime.toIso8601String(),
      'deliveryStatus': deliveryStatus.name,
      'failureReason': failureReason,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON representation
  factory NotificationDeliveryEvent.fromJson(Map<String, dynamic> json) {
    return NotificationDeliveryEvent(
      id: json['id'] as String,
      notificationId: json['notificationId'] as String,
      channelKey: NotificationChannelKey.values.firstWhere(
        (key) => key.name == json['channelKey'],
        orElse: () => NotificationChannelKey.general,
      ),
      deliveryTime: DateTime.parse(json['deliveryTime'] as String),
      deliveryStatus: NotificationDeliveryStatus.values.firstWhere(
        (status) => status.name == json['deliveryStatus'],
        orElse: () => NotificationDeliveryStatus.pending,
      ),
      failureReason: json['failureReason'] as String?,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  String toString() {
    return 'NotificationDeliveryEvent(id: $id, notificationId: $notificationId, status: $deliveryStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationDeliveryEvent &&
        super == other &&
        other.notificationId == notificationId &&
        other.channelKey == channelKey &&
        other.deliveryTime == deliveryTime &&
        other.deliveryStatus == deliveryStatus &&
        other.failureReason == failureReason;
  }

  @override
  int get hashCode {
    return Object.hash(super.hashCode, notificationId, channelKey, deliveryTime, deliveryStatus, failureReason);
  }
}

/// Notification Interaction Event
///
/// Analytics event for tracking user interactions with notifications.
@immutable
class NotificationInteractionEvent extends NotificationAnalyticsEvent {
  /// ID of the notification that was interacted with
  final String notificationId;

  /// Type of interaction performed by the user
  final NotificationInteractionType interactionType;

  /// ID of the specific action performed (if applicable)
  final String? actionId;

  /// Time when the interaction occurred
  final DateTime interactionTime;

  /// Create a notification interaction event
  const NotificationInteractionEvent({
    required super.id,
    required this.notificationId,
    required this.interactionType,
    this.actionId,
    required this.interactionTime,
    super.metadata = const {},
    required super.timestamp,
  });

  /// Create a copy with updated properties
  NotificationInteractionEvent copyWith({
    String? id,
    String? notificationId,
    NotificationInteractionType? interactionType,
    String? actionId,
    DateTime? interactionTime,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
  }) {
    return NotificationInteractionEvent(
      id: id ?? this.id,
      notificationId: notificationId ?? this.notificationId,
      interactionType: interactionType ?? this.interactionType,
      actionId: actionId ?? this.actionId,
      interactionTime: interactionTime ?? this.interactionTime,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'notificationId': notificationId,
      'interactionType': interactionType.name,
      'actionId': actionId,
      'interactionTime': interactionTime.toIso8601String(),
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON representation
  factory NotificationInteractionEvent.fromJson(Map<String, dynamic> json) {
    return NotificationInteractionEvent(
      id: json['id'] as String,
      notificationId: json['notificationId'] as String,
      interactionType: NotificationInteractionType.values.firstWhere(
        (type) => type.name == json['interactionType'],
        orElse: () => NotificationInteractionType.opened,
      ),
      actionId: json['actionId'] as String?,
      interactionTime: DateTime.parse(json['interactionTime'] as String),
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  String toString() {
    return 'NotificationInteractionEvent(id: $id, notificationId: $notificationId, type: $interactionType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationInteractionEvent &&
        super == other &&
        other.notificationId == notificationId &&
        other.interactionType == interactionType &&
        other.actionId == actionId &&
        other.interactionTime == interactionTime;
  }

  @override
  int get hashCode {
    return Object.hash(super.hashCode, notificationId, interactionType, actionId, interactionTime);
  }
}

/// Notification Error Event
///
/// Analytics event for tracking errors in the notification system.
@immutable
class NotificationErrorEvent extends NotificationAnalyticsEvent {
  /// Type of error that occurred
  final String errorType;

  /// Detailed error message
  final String errorMessage;

  /// ID of the notification related to the error (if applicable)
  final String? notificationId;

  /// Channel where the error occurred (if applicable)
  final NotificationChannelKey? channelKey;

  /// Create a notification error event
  const NotificationErrorEvent({
    required super.id,
    required this.errorType,
    required this.errorMessage,
    this.notificationId,
    this.channelKey,
    super.metadata = const {},
    required super.timestamp,
  });

  /// Create a copy with updated properties
  NotificationErrorEvent copyWith({
    String? id,
    String? errorType,
    String? errorMessage,
    String? notificationId,
    NotificationChannelKey? channelKey,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
  }) {
    return NotificationErrorEvent(
      id: id ?? this.id,
      errorType: errorType ?? this.errorType,
      errorMessage: errorMessage ?? this.errorMessage,
      notificationId: notificationId ?? this.notificationId,
      channelKey: channelKey ?? this.channelKey,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'errorType': errorType,
      'errorMessage': errorMessage,
      'notificationId': notificationId,
      'channelKey': channelKey?.name,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON representation
  factory NotificationErrorEvent.fromJson(Map<String, dynamic> json) {
    return NotificationErrorEvent(
      id: json['id'] as String,
      errorType: json['errorType'] as String,
      errorMessage: json['errorMessage'] as String,
      notificationId: json['notificationId'] as String?,
      channelKey: json['channelKey'] != null
          ? NotificationChannelKey.values.firstWhere(
              (key) => key.name == json['channelKey'],
              orElse: () => NotificationChannelKey.general,
            )
          : null,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  String toString() {
    return 'NotificationErrorEvent(id: $id, errorType: $errorType, message: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationErrorEvent &&
        super == other &&
        other.errorType == errorType &&
        other.errorMessage == errorMessage &&
        other.notificationId == notificationId &&
        other.channelKey == channelKey;
  }

  @override
  int get hashCode {
    return Object.hash(super.hashCode, errorType, errorMessage, notificationId, channelKey);
  }
}

/// Notification Performance Event
///
/// Analytics event for tracking performance metrics of notification operations.
@immutable
class NotificationPerformanceEvent extends NotificationAnalyticsEvent {
  /// Type of operation being measured
  final String operationType;

  /// Time taken to complete the operation
  final Duration processingTime;

  /// ID of the notification related to the operation (if applicable)
  final String? notificationId;

  /// Channel where the operation occurred (if applicable)
  final NotificationChannelKey? channelKey;

  /// Create a notification performance event
  const NotificationPerformanceEvent({
    required super.id,
    required this.operationType,
    required this.processingTime,
    this.notificationId,
    this.channelKey,
    super.metadata = const {},
    required super.timestamp,
  });

  /// Create a copy with updated properties
  NotificationPerformanceEvent copyWith({
    String? id,
    String? operationType,
    Duration? processingTime,
    String? notificationId,
    NotificationChannelKey? channelKey,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
  }) {
    return NotificationPerformanceEvent(
      id: id ?? this.id,
      operationType: operationType ?? this.operationType,
      processingTime: processingTime ?? this.processingTime,
      notificationId: notificationId ?? this.notificationId,
      channelKey: channelKey ?? this.channelKey,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operationType': operationType,
      'processingTime': processingTime.inMilliseconds,
      'notificationId': notificationId,
      'channelKey': channelKey?.name,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON representation
  factory NotificationPerformanceEvent.fromJson(Map<String, dynamic> json) {
    return NotificationPerformanceEvent(
      id: json['id'] as String,
      operationType: json['operationType'] as String,
      processingTime: Duration(milliseconds: json['processingTime'] as int),
      notificationId: json['notificationId'] as String?,
      channelKey: json['channelKey'] != null
          ? NotificationChannelKey.values.firstWhere(
              (key) => key.name == json['channelKey'],
              orElse: () => NotificationChannelKey.general,
            )
          : null,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  String toString() {
    return 'NotificationPerformanceEvent(id: $id, operation: $operationType, time: ${processingTime.inMilliseconds}ms)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationPerformanceEvent &&
        super == other &&
        other.operationType == operationType &&
        other.processingTime == processingTime &&
        other.notificationId == notificationId &&
        other.channelKey == channelKey;
  }

  @override
  int get hashCode {
    return Object.hash(super.hashCode, operationType, processingTime, notificationId, channelKey);
  }
}

/// Notification Analytics Configuration
///
/// Configuration settings for the notification analytics service.
@immutable
class NotificationAnalyticsConfig {
  /// Whether to enable delivery tracking
  final bool enableDeliveryTracking;

  /// Whether to enable interaction tracking
  final bool enableInteractionTracking;

  /// Whether to enable error tracking
  final bool enableErrorTracking;

  /// Whether to enable performance tracking
  final bool enablePerformanceTracking;

  /// Whether to enable batch processing
  final bool enableBatchProcessing;

  /// Interval for batch processing
  final Duration batchProcessingInterval;

  /// Size of each batch for processing
  final int batchSize;

  /// Data retention period
  final Duration dataRetentionPeriod;

  /// Whether to respect user privacy preferences
  final bool respectPrivacyPreferences;

  /// Create notification analytics configuration
  const NotificationAnalyticsConfig({
    this.enableDeliveryTracking = true,
    this.enableInteractionTracking = true,
    this.enableErrorTracking = true,
    this.enablePerformanceTracking = true,
    this.enableBatchProcessing = true,
    this.batchProcessingInterval = const Duration(minutes: 5),
    this.batchSize = 100,
    this.dataRetentionPeriod = const Duration(days: 30),
    this.respectPrivacyPreferences = true,
  });

  /// Create default configuration
  factory NotificationAnalyticsConfig.defaultConfig() {
    return const NotificationAnalyticsConfig();
  }

  /// Create privacy-focused configuration
  factory NotificationAnalyticsConfig.privacyFocused() {
    return const NotificationAnalyticsConfig(
      enableDeliveryTracking: true,
      enableInteractionTracking: false,
      enableErrorTracking: true,
      enablePerformanceTracking: false,
      dataRetentionPeriod: Duration(days: 7),
      respectPrivacyPreferences: true,
    );
  }

  /// Create a copy with updated properties
  NotificationAnalyticsConfig copyWith({
    bool? enableDeliveryTracking,
    bool? enableInteractionTracking,
    bool? enableErrorTracking,
    bool? enablePerformanceTracking,
    bool? enableBatchProcessing,
    Duration? batchProcessingInterval,
    int? batchSize,
    Duration? dataRetentionPeriod,
    bool? respectPrivacyPreferences,
  }) {
    return NotificationAnalyticsConfig(
      enableDeliveryTracking: enableDeliveryTracking ?? this.enableDeliveryTracking,
      enableInteractionTracking: enableInteractionTracking ?? this.enableInteractionTracking,
      enableErrorTracking: enableErrorTracking ?? this.enableErrorTracking,
      enablePerformanceTracking: enablePerformanceTracking ?? this.enablePerformanceTracking,
      enableBatchProcessing: enableBatchProcessing ?? this.enableBatchProcessing,
      batchProcessingInterval: batchProcessingInterval ?? this.batchProcessingInterval,
      batchSize: batchSize ?? this.batchSize,
      dataRetentionPeriod: dataRetentionPeriod ?? this.dataRetentionPeriod,
      respectPrivacyPreferences: respectPrivacyPreferences ?? this.respectPrivacyPreferences,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'enableDeliveryTracking': enableDeliveryTracking,
      'enableInteractionTracking': enableInteractionTracking,
      'enableErrorTracking': enableErrorTracking,
      'enablePerformanceTracking': enablePerformanceTracking,
      'enableBatchProcessing': enableBatchProcessing,
      'batchProcessingInterval': batchProcessingInterval.inMilliseconds,
      'batchSize': batchSize,
      'dataRetentionPeriod': dataRetentionPeriod.inDays,
      'respectPrivacyPreferences': respectPrivacyPreferences,
    };
  }

  /// Create from JSON representation
  factory NotificationAnalyticsConfig.fromJson(Map<String, dynamic> json) {
    return NotificationAnalyticsConfig(
      enableDeliveryTracking: json['enableDeliveryTracking'] as bool? ?? true,
      enableInteractionTracking: json['enableInteractionTracking'] as bool? ?? true,
      enableErrorTracking: json['enableErrorTracking'] as bool? ?? true,
      enablePerformanceTracking: json['enablePerformanceTracking'] as bool? ?? true,
      enableBatchProcessing: json['enableBatchProcessing'] as bool? ?? true,
      batchProcessingInterval: Duration(milliseconds: json['batchProcessingInterval'] as int? ?? 300000),
      batchSize: json['batchSize'] as int? ?? 100,
      dataRetentionPeriod: Duration(days: json['dataRetentionPeriod'] as int? ?? 30),
      respectPrivacyPreferences: json['respectPrivacyPreferences'] as bool? ?? true,
    );
  }

  @override
  String toString() {
    return 'NotificationAnalyticsConfig(delivery: $enableDeliveryTracking, interaction: $enableInteractionTracking, error: $enableErrorTracking, performance: $enablePerformanceTracking)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationAnalyticsConfig &&
        other.enableDeliveryTracking == enableDeliveryTracking &&
        other.enableInteractionTracking == enableInteractionTracking &&
        other.enableErrorTracking == enableErrorTracking &&
        other.enablePerformanceTracking == enablePerformanceTracking &&
        other.enableBatchProcessing == enableBatchProcessing &&
        other.batchProcessingInterval == batchProcessingInterval &&
        other.batchSize == batchSize &&
        other.dataRetentionPeriod == dataRetentionPeriod &&
        other.respectPrivacyPreferences == respectPrivacyPreferences;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableDeliveryTracking,
      enableInteractionTracking,
      enableErrorTracking,
      enablePerformanceTracking,
      enableBatchProcessing,
      batchProcessingInterval,
      batchSize,
      dataRetentionPeriod,
      respectPrivacyPreferences,
    );
  }
}

/// Notification Delivery Metrics
///
/// Metrics related to notification delivery performance and success rates.
@immutable
class NotificationDeliveryMetrics {
  /// Total number of delivery attempts
  final int totalDeliveries;

  /// Number of successful deliveries
  final int successfulDeliveries;

  /// Number of failed deliveries
  final int failedDeliveries;

  /// Success rate (0.0 to 1.0)
  final double successRate;

  /// Average time taken for delivery
  final Duration averageDeliveryTime;

  /// Create notification delivery metrics
  const NotificationDeliveryMetrics({
    required this.totalDeliveries,
    required this.successfulDeliveries,
    required this.failedDeliveries,
    required this.successRate,
    required this.averageDeliveryTime,
  });

  /// Create empty metrics
  factory NotificationDeliveryMetrics.empty() {
    return const NotificationDeliveryMetrics(
      totalDeliveries: 0,
      successfulDeliveries: 0,
      failedDeliveries: 0,
      successRate: 0.0,
      averageDeliveryTime: Duration.zero,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'totalDeliveries': totalDeliveries,
      'successfulDeliveries': successfulDeliveries,
      'failedDeliveries': failedDeliveries,
      'successRate': successRate,
      'averageDeliveryTime': averageDeliveryTime.inMilliseconds,
    };
  }

  /// Create from JSON representation
  factory NotificationDeliveryMetrics.fromJson(Map<String, dynamic> json) {
    return NotificationDeliveryMetrics(
      totalDeliveries: json['totalDeliveries'] as int,
      successfulDeliveries: json['successfulDeliveries'] as int,
      failedDeliveries: json['failedDeliveries'] as int,
      successRate: (json['successRate'] as num).toDouble(),
      averageDeliveryTime: Duration(milliseconds: json['averageDeliveryTime'] as int),
    );
  }

  @override
  String toString() {
    return 'NotificationDeliveryMetrics(total: $totalDeliveries, success: $successfulDeliveries, rate: ${(successRate * 100).toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationDeliveryMetrics &&
        other.totalDeliveries == totalDeliveries &&
        other.successfulDeliveries == successfulDeliveries &&
        other.failedDeliveries == failedDeliveries &&
        other.successRate == successRate &&
        other.averageDeliveryTime == averageDeliveryTime;
  }

  @override
  int get hashCode {
    return Object.hash(totalDeliveries, successfulDeliveries, failedDeliveries, successRate, averageDeliveryTime);
  }
}

/// Notification Engagement Metrics
///
/// Metrics related to user engagement with notifications.
@immutable
class NotificationEngagementMetrics {
  /// Total number of user interactions
  final int totalInteractions;

  /// Number of notifications opened
  final int openedNotifications;

  /// Number of notifications clicked
  final int clickedNotifications;

  /// Open rate (0.0 to 1.0)
  final double openRate;

  /// Click-through rate (0.0 to 1.0)
  final double clickThroughRate;

  /// Overall engagement rate (0.0 to 1.0)
  final double engagementRate;

  /// Create notification engagement metrics
  const NotificationEngagementMetrics({
    required this.totalInteractions,
    required this.openedNotifications,
    required this.clickedNotifications,
    required this.openRate,
    required this.clickThroughRate,
    required this.engagementRate,
  });

  /// Create empty metrics
  factory NotificationEngagementMetrics.empty() {
    return const NotificationEngagementMetrics(
      totalInteractions: 0,
      openedNotifications: 0,
      clickedNotifications: 0,
      openRate: 0.0,
      clickThroughRate: 0.0,
      engagementRate: 0.0,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'totalInteractions': totalInteractions,
      'openedNotifications': openedNotifications,
      'clickedNotifications': clickedNotifications,
      'openRate': openRate,
      'clickThroughRate': clickThroughRate,
      'engagementRate': engagementRate,
    };
  }

  /// Create from JSON representation
  factory NotificationEngagementMetrics.fromJson(Map<String, dynamic> json) {
    return NotificationEngagementMetrics(
      totalInteractions: json['totalInteractions'] as int,
      openedNotifications: json['openedNotifications'] as int,
      clickedNotifications: json['clickedNotifications'] as int,
      openRate: (json['openRate'] as num).toDouble(),
      clickThroughRate: (json['clickThroughRate'] as num).toDouble(),
      engagementRate: (json['engagementRate'] as num).toDouble(),
    );
  }

  @override
  String toString() {
    return 'NotificationEngagementMetrics(interactions: $totalInteractions, open: ${(openRate * 100).toStringAsFixed(1)}%, ctr: ${(clickThroughRate * 100).toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationEngagementMetrics &&
        other.totalInteractions == totalInteractions &&
        other.openedNotifications == openedNotifications &&
        other.clickedNotifications == clickedNotifications &&
        other.openRate == openRate &&
        other.clickThroughRate == clickThroughRate &&
        other.engagementRate == engagementRate;
  }

  @override
  int get hashCode {
    return Object.hash(
      totalInteractions,
      openedNotifications,
      clickedNotifications,
      openRate,
      clickThroughRate,
      engagementRate,
    );
  }
}

/// Notification Analytics Summary
///
/// Comprehensive analytics summary for the unified notification system.
@immutable
class NotificationAnalytics {
  /// Delivery metrics
  final NotificationDeliveryMetrics deliveryMetrics;

  /// Engagement metrics
  final NotificationEngagementMetrics engagementMetrics;

  /// Total number of notifications scheduled
  final int totalScheduled;

  /// Total number of notifications cancelled
  final int totalCancelled;

  /// Total number of errors encountered
  final int totalErrors;

  /// Last update timestamp
  final DateTime lastUpdated;

  /// Create notification analytics summary
  const NotificationAnalytics({
    required this.deliveryMetrics,
    required this.engagementMetrics,
    required this.totalScheduled,
    required this.totalCancelled,
    required this.totalErrors,
    required this.lastUpdated,
  });

  /// Create initial/empty analytics
  factory NotificationAnalytics.initial() {
    return NotificationAnalytics(
      deliveryMetrics: NotificationDeliveryMetrics.empty(),
      engagementMetrics: NotificationEngagementMetrics.empty(),
      totalScheduled: 0,
      totalCancelled: 0,
      totalErrors: 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a copy with updated properties
  NotificationAnalytics copyWith({
    NotificationDeliveryMetrics? deliveryMetrics,
    NotificationEngagementMetrics? engagementMetrics,
    int? totalScheduled,
    int? totalCancelled,
    int? totalErrors,
    DateTime? lastUpdated,
  }) {
    return NotificationAnalytics(
      deliveryMetrics: deliveryMetrics ?? this.deliveryMetrics,
      engagementMetrics: engagementMetrics ?? this.engagementMetrics,
      totalScheduled: totalScheduled ?? this.totalScheduled,
      totalCancelled: totalCancelled ?? this.totalCancelled,
      totalErrors: totalErrors ?? this.totalErrors,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'deliveryMetrics': deliveryMetrics.toJson(),
      'engagementMetrics': engagementMetrics.toJson(),
      'totalScheduled': totalScheduled,
      'totalCancelled': totalCancelled,
      'totalErrors': totalErrors,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create from JSON representation
  factory NotificationAnalytics.fromJson(Map<String, dynamic> json) {
    return NotificationAnalytics(
      deliveryMetrics: NotificationDeliveryMetrics.fromJson(json['deliveryMetrics'] as Map<String, dynamic>),
      engagementMetrics: NotificationEngagementMetrics.fromJson(json['engagementMetrics'] as Map<String, dynamic>),
      totalScheduled: json['totalScheduled'] as int,
      totalCancelled: json['totalCancelled'] as int,
      totalErrors: json['totalErrors'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  @override
  String toString() {
    return 'NotificationAnalytics(scheduled: $totalScheduled, cancelled: $totalCancelled, errors: $totalErrors)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationAnalytics &&
        other.deliveryMetrics == deliveryMetrics &&
        other.engagementMetrics == engagementMetrics &&
        other.totalScheduled == totalScheduled &&
        other.totalCancelled == totalCancelled &&
        other.totalErrors == totalErrors &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(deliveryMetrics, engagementMetrics, totalScheduled, totalCancelled, totalErrors, lastUpdated);
  }
}
