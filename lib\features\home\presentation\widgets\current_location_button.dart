import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/providers/location_providers.dart';
import '../../../../core/providers/master_location_provider.dart';
import '../../../../core/utils/l10n.dart';
import '../../../../core/utils/result.dart';

/// A floating action button for centering the map on user's current location
class CurrentLocationButton extends ConsumerStatefulWidget {
  /// The Google Maps controller to animate the camera
  final GoogleMapController? mapController;

  /// Callback when location is being fetched (for loading state)
  final VoidCallback? onLocationFetching;

  /// Callback when location fetch is complete
  final VoidCallback? onLocationFetched;

  /// Optional tutorial key for highlighting this button
  final Key? tutorialKey;

  /// Constructor
  const CurrentLocationButton({
    super.key,
    required this.mapController,
    this.onLocationFetching,
    this.onLocationFetched,
    this.tutorialKey,
  });

  @override
  ConsumerState<CurrentLocationButton> createState() => _CurrentLocationButtonState();
}

class _CurrentLocationButtonState extends ConsumerState<CurrentLocationButton> with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 1000), vsync: this);
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.linear));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Handle the current location button tap
  Future<void> _onLocationButtonTap() async {
    // Prevent multiple taps while loading
    if (_isLoading || widget.mapController == null) return;

    setState(() {
      _isLoading = true;
    });

    // Start loading animation
    unawaited(_animationController.repeat());
    widget.onLocationFetching?.call();

    try {
      // Context7 MCP: Check location permission status using master provider
      final isLocationAvailable = ref.read(isLocationServiceReadyProvider);

      if (!isLocationAvailable) {
        // Location is not available, request permission for current location feature
        // For explicit user action, always show system permission dialog
        final permissionGranted = await _requestLocationPermissionForUserAction();

        // If permission was not granted, reset map to default view
        if (!permissionGranted) {
          await _resetMapView();
          if (mounted) {
            _showLocationNotAvailableSnackBar();
          }
          return;
        }

        // Context7 MCP: Permission was granted, initialize location services properly
        await _initializeLocationServicesAfterPermissionGrant();
      }

      // Context7 MCP: Try to get fresh location using master provider
      final locationManager = ref.read(masterLocationManagerProvider.notifier);
      await locationManager.getCurrentLocation(useCache: false);

      // Get the current location from the master provider
      final currentLocation = ref.read(currentLocationSelectorProvider);
      Position? userPosition;

      if (currentLocation != null) {
        userPosition = Position(
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          timestamp: currentLocation.timestamp ?? DateTime.now(),
          accuracy: currentLocation.accuracy,
          altitude: currentLocation.altitude ?? 0.0,
          altitudeAccuracy: 0.0,
          heading: currentLocation.heading ?? 0.0,
          headingAccuracy: 0.0,
          speed: currentLocation.speed ?? 0.0,
          speedAccuracy: 0.0,
        );
        debugPrint('📍 Current location button: Got fresh location');
      } else {
        debugPrint('📍 Current location button: Failed to get fresh location');
      }

      if (userPosition != null && mounted) {
        // Animate camera to user location
        await widget.mapController!.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(userPosition.latitude, userPosition.longitude),
              zoom: 16.0, // Zoom in closer for current location
              tilt: AppConstants.defaultMapTilt,
              bearing: AppConstants.defaultMapBearing,
            ),
          ),
        );

        if (mounted) {
          _showLocationCenteredSnackBar();
        }
      } else {
        // Fallback to default view if location is not available
        await _resetMapView();
        if (mounted) {
          _showLocationNotAvailableSnackBar();
        }
      }
    } on Exception catch (e) {
      debugPrint('Error getting current location: $e');
      // Fallback to default view on error
      await _resetMapView();
      if (mounted) {
        _showLocationNotAvailableSnackBar();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _animationController.stop();
        _animationController.reset();
        widget.onLocationFetched?.call();
      }
    }
  }

  /// Reset map view to default Bahrain location
  Future<void> _resetMapView() async {
    if (widget.mapController != null) {
      await widget.mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          const CameraPosition(
            target: LatLng(26.05, 50.55), // Center of Bahrain
            zoom: 10.3, // Default zoom level
            tilt: AppConstants.defaultMapTilt,
            bearing: AppConstants.defaultMapBearing,
          ),
        ),
      );
    }
  }

  /// Show snack bar when location is centered
  void _showLocationCenteredSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.l10n?.centerOnLocation ?? 'Centered on your location'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show snack bar when location is not available
  void _showLocationNotAvailableSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.l10n?.resetMapView ?? 'Reset map view'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Initialize location services after permission is granted
  ///
  /// Context7 MCP: Implements proper state synchronization and location initialization
  /// following best practices for immediate location availability after permission grant
  Future<void> _initializeLocationServicesAfterPermissionGrant() async {
    try {
      debugPrint('📍 CurrentLocationButton: Initializing location services after permission grant');

      // Step 1: Refresh location-related providers to trigger state update
      _refreshLocationProvidersAfterPermissionGrant();

      // Step 2: Wait for provider state to propagate
      await Future.delayed(const Duration(milliseconds: 300));

      // Step 3: Trigger immediate location fetch using master location manager
      final locationManager = ref.read(masterLocationManagerProvider.notifier);
      debugPrint('📍 CurrentLocationButton: Requesting fresh location after permission grant');

      // Request fresh location to populate providers immediately
      final locationResult = await locationManager.getCurrentLocation(useCache: false);

      if (locationResult.isSuccess) {
        debugPrint('✅ CurrentLocationButton: Fresh location obtained after permission grant');

        // Step 4: Wait for location to propagate to all providers
        await Future.delayed(const Duration(milliseconds: 200));

        // Step 5: Verify location services are ready
        final isReady = ref.read(isLocationServiceReadyProvider);
        debugPrint('📍 CurrentLocationButton: Location services ready status: $isReady');

        if (!isReady) {
          debugPrint('⚠️ CurrentLocationButton: Location services not ready, waiting additional time');
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } else {
        debugPrint('❌ CurrentLocationButton: Failed to get fresh location: ${locationResult.errorOrNull}');
      }

      debugPrint('✅ CurrentLocationButton: Location services initialization completed');
    } on Exception catch (e) {
      debugPrint('❌ CurrentLocationButton: Error initializing location services: $e');
    }
  }

  /// Refresh location-related providers after permission is granted
  ///
  /// Implements Context7 MCP best practices for provider state management
  void _refreshLocationProvidersAfterPermissionGrant() {
    try {
      debugPrint('📍 CurrentLocationButton: Refreshing location providers after permission grant');

      // Context7 MCP: Invalidate unified location providers to refresh location state
      ref.invalidate(unifiedLocationManagerProvider);
      ref.invalidate(currentLocationProvider);
      ref.invalidate(masterLocationManagerProvider);

      debugPrint('✅ CurrentLocationButton: Location providers refreshed successfully');
    } on Exception catch (e) {
      debugPrint('❌ CurrentLocationButton: Error refreshing location providers: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    return Container(
      margin: const EdgeInsets.only(top: 16.0, right: 16.0),
      child: FloatingActionButton(
        key: widget.tutorialKey,
        onPressed: _isLoading ? null : _onLocationButtonTap,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: primaryColor,
        elevation: 4.0,
        mini: true,
        tooltip: context.l10n?.currentLocation ?? 'Current Location',
        child: _isLoading
            ? AnimatedBuilder(
                animation: _rotationAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _rotationAnimation.value * 2.0 * 3.14159,
                    child: Icon(Icons.my_location, color: primaryColor.withValues(alpha: 0.7), size: 20.0),
                  );
                },
              )
            : Icon(Icons.my_location, color: primaryColor, size: 20.0),
      ),
    );
  }

  /// Request location permission directly for explicit user action
  /// This bypasses complex permission service logic and always shows system dialog
  /// Returns true if permission was granted, false otherwise
  Future<bool> _requestLocationPermissionForUserAction() async {
    try {
      // First check current permission status
      final currentPermission = await Geolocator.checkPermission();
      debugPrint('🔔 Current location button permission status: $currentPermission');

      // If already granted, return true
      if (currentPermission == LocationPermission.whileInUse || currentPermission == LocationPermission.always) {
        debugPrint('✅ Permission already granted for current location');
        return true;
      }

      // Check if permission is permanently denied
      if (currentPermission == LocationPermission.deniedForever) {
        debugPrint('⚠️ Permission permanently denied for current location, opening app settings');
        // Show dialog to inform user and offer to open settings
        if (mounted) {
          await _showOpenSettingsDialog();
        }
        return false;
      }

      // Permission not granted but can be requested, request it directly
      debugPrint('🔔 Requesting location permission for current location...');
      final permission = await Geolocator.requestPermission();
      debugPrint('🔔 Current location permission result: $permission');

      // Check if permission was granted
      if (permission == LocationPermission.whileInUse || permission == LocationPermission.always) {
        debugPrint('✅ Location permission granted for current location');
        return true;
      } else if (permission == LocationPermission.deniedForever) {
        // User selected "Don't ask again", offer to open settings
        debugPrint('⚠️ Permission permanently denied after request for current location');
        if (mounted) {
          await _showOpenSettingsDialog();
        }
        return false;
      } else {
        // Permission denied but not permanently
        debugPrint('❌ Location permission denied by user for current location: $permission');
        return false;
      }
    } on Exception catch (e) {
      debugPrint('Error requesting location permission: $e');
      return false;
    }
  }

  /// Show dialog to open app settings for location permission
  Future<void> _showOpenSettingsDialog() async {
    if (!mounted) return;

    return showDialog<void>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(context.l10n?.locationPermissionRequired ?? 'Location Permission Required'),
          content: Text(
            context.l10n?.locationPermissionDenied ??
                'Location permission is required to show your current location. Please enable location permission in app settings.',
          ),
          actions: <Widget>[
            TextButton(
              child: Text(context.l10n?.cancel ?? 'Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: Text(context.l10n?.openLocationSettings ?? 'Open Settings'),
              onPressed: () async {
                Navigator.of(context).pop();
                await Geolocator.openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }
}
